#!/usr/bin/env python3
"""
本地测试服务器 - 用于测试Tera启动器
提供模拟的API端点来测试启动器功能
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import os
import urllib.parse
from datetime import datetime
import mimetypes

class TeraTestServer(BaseHTTPRequestHandler):
    def do_GET(self):
        """处理GET请求"""
        parsed_path = urllib.parse.urlparse(self.path)
        path = parsed_path.path
        query = urllib.parse.parse_qs(parsed_path.query)
        
        print(f"[{datetime.now().strftime('%H:%M:%S')}] GET {self.path}")
        
        # 路由处理
        if path == '/tera/ServerList.json':
            self.handle_server_list(query)
        elif path == '/tera/launcher/hash-file.json':
            self.handle_hash_file()
        elif path.startswith('/public/files/'):
            self.handle_file_download(path)
        elif path == '/':
            self.handle_status()
        else:
            self.send_404()
    
    def do_POST(self):
        """处理POST请求"""
        parsed_path = urllib.parse.urlparse(self.path)
        path = parsed_path.path
        
        print(f"[{datetime.now().strftime('%H:%M:%S')}] POST {self.path}")
        
        if path == '/tera/LauncherLoginAction':
            self.handle_login()
        else:
            self.send_404()
    
    def handle_server_list(self, query):
        """返回服务器列表"""
        lang = query.get('lang', ['en'])[0]
        sort = query.get('sort', ['3'])[0]
        
        server_list = {
            "servers": [
                {
                    "id": 1,
                    "name": "Test Server",
                    "category": "PvE",
                    "title": "测试服务器",
                    "queue": "0",
                    "population": "High",
                    "address": "127.0.0.1",
                    "port": 10001,
                    "available": 1,
                    "unavailable_message": ""
                },
                {
                    "id": 2,
                    "name": "Dev Server",
                    "category": "PvP",
                    "title": "开发服务器",
                    "queue": "0",
                    "population": "Medium",
                    "address": "127.0.0.1",
                    "port": 10002,
                    "available": 1,
                    "unavailable_message": ""
                }
            ],
            "sort_criterion": int(sort)
        }
        
        self.send_json_response(server_list)
    
    def handle_hash_file(self):
        """返回文件哈希列表"""
        hash_data = {
            "files": [
                {
                    "path": "S1Game/CookedPC/Art_Data.gpk",
                    "hash": "abc123def456",
                    "size": 1024000,
                    "url": "http://localhost:8000/public/files/S1Game/CookedPC/Art_Data.gpk"
                },
                {
                    "path": "S1Game/Config/DefaultEngine.ini",
                    "hash": "def456ghi789",
                    "size": 2048,
                    "url": "http://localhost:8000/public/files/S1Game/Config/DefaultEngine.ini"
                }
            ],
            "version": "1.0.0",
            "timestamp": datetime.now().isoformat()
        }
        
        self.send_json_response(hash_data)
    
    def handle_login(self):
        """处理登录请求"""
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)

        try:
            # 处理表单数据 (application/x-www-form-urlencoded)
            if self.headers.get('Content-Type') == 'application/x-www-form-urlencoded':
                login_data = urllib.parse.parse_qs(post_data.decode('utf-8'))
                login_data = {k: v[0] if isinstance(v, list) else v for k, v in login_data.items()}
            elif self.headers.get('Content-Type') == 'application/json':
                login_data = json.loads(post_data.decode('utf-8'))
            else:
                # 默认按表单数据处理
                login_data = urllib.parse.parse_qs(post_data.decode('utf-8'))
                login_data = {k: v[0] if isinstance(v, list) else v for k, v in login_data.items()}

            print(f"Login attempt: {login_data}")

            # 获取用户名 - 启动器使用 'login' 字段而不是 'username'
            username = login_data.get("login", login_data.get("username", "TestUser"))

            # 模拟登录响应 - 使用启动器期望的格式
            response = {
                "Return": True,
                "Msg": "success",
                "user_no": 12345,
                "user_name": username,
                "auth_key": "test_auth_key_123456",
                "character_count": "3"
            }
            
            self.send_json_response(response)
            
        except Exception as e:
            print(f"Login error: {e}")
            error_response = {
                "success": False,
                "message": f"Login failed: {str(e)}"
            }
            self.send_json_response(error_response, status=400)
    
    def handle_file_download(self, path):
        """处理文件下载请求"""
        # 移除 /public/files/ 前缀
        file_path = path.replace('/public/files/', '')
        
        # 创建一个模拟文件内容
        mock_content = f"Mock file content for: {file_path}\nGenerated at: {datetime.now()}\n"
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/octet-stream')
        self.send_header('Content-Length', str(len(mock_content.encode())))
        self.send_header('Content-Disposition', f'attachment; filename="{os.path.basename(file_path)}"')
        self.end_headers()
        
        self.wfile.write(mock_content.encode())
    
    def handle_status(self):
        """返回服务器状态页面"""
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Tera Test Server</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .status { color: green; font-weight: bold; }
                .endpoint { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
                .method { color: #007acc; font-weight: bold; }
            </style>
        </head>
        <body>
            <h1>Tera Test Server</h1>
            <p class="status">✅ Server is running on http://localhost:8000</p>
            
            <h2>Available Endpoints:</h2>
            
            <div class="endpoint">
                <span class="method">GET</span> /tera/ServerList.json?lang=en&sort=3
                <br><small>Returns server list for the launcher</small>
            </div>
            
            <div class="endpoint">
                <span class="method">GET</span> /tera/launcher/hash-file.json
                <br><small>Returns file hash information for updates</small>
            </div>
            
            <div class="endpoint">
                <span class="method">POST</span> /tera/LauncherLoginAction
                <br><small>Handles login requests</small>
            </div>
            
            <div class="endpoint">
                <span class="method">GET</span> /public/files/*
                <br><small>Serves game files for download</small>
            </div>
            
            <h2>Test Links:</h2>
            <ul>
                <li><a href="/tera/ServerList.json?lang=en&sort=3">Server List</a></li>
                <li><a href="/tera/launcher/hash-file.json">Hash File</a></li>
            </ul>
            
            <p><small>Started at: """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + """</small></p>
        </body>
        </html>
        """
        
        self.send_response(200)
        self.send_header('Content-Type', 'text/html; charset=utf-8')
        self.send_header('Content-Length', str(len(html_content.encode())))
        self.end_headers()
        
        self.wfile.write(html_content.encode())
    
    def send_json_response(self, data, status=200):
        """发送JSON响应"""
        json_data = json.dumps(data, indent=2, ensure_ascii=False)
        
        self.send_response(status)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Content-Length', str(len(json_data.encode())))
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        self.wfile.write(json_data.encode())
    
    def send_404(self):
        """发送404响应"""
        self.send_response(404)
        self.send_header('Content-Type', 'text/plain')
        self.end_headers()
        self.wfile.write(b'404 Not Found')
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        pass  # 我们在do_GET和do_POST中已经有了自定义日志

def run_server(port=8000):
    """启动测试服务器"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, TeraTestServer)
    
    print(f"🚀 Tera Test Server starting on http://localhost:{port}")
    print(f"📝 Access status page: http://localhost:{port}")
    print(f"🔧 API endpoints available for testing")
    print(f"⏹️  Press Ctrl+C to stop the server")
    print("-" * 50)
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
        httpd.server_close()

if __name__ == '__main__':
    run_server()
