{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"codegen\", \"config-json5\", \"config-toml\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 9570484432207332980, "profile": 2225463790103693989, "path": 14648466730814367167, "deps": [[533142347765177280, "anyhow", false, 15134541861625046067], [1144798529435568546, "tauri_utils", false, 6005736629602156391], [2455542577989222201, "json_patch", false, 9678803638844142298], [3851720982022213547, "semver", false, 5988392385646627132], [4450062412064442726, "dirs_next", false, 3001937851887828194], [4704408070981680310, "serde_json", false, 17047230535494805844], [7468248713591957673, "cargo_toml", false, 657667869080555104], [13077543566650298139, "heck", false, 8749295945489699306], [14189313126492979171, "tauri_winres", false, 5375678501722952151], [15622660310229662834, "walkdir", false, 5583928852631741149], [17174345729924723953, "serde", false, 2591028312899638256]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-83dfd2cf1ab0df4b\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}