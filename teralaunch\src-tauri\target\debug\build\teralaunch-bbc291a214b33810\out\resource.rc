#pragma code_page(65001)
1 VERSIONINFO
FILETYPE 0x1
FILESUBTYPE 0x0
FILEOS 0x40004
FILEFLAGSMASK 0x3f
PRODUCTVERSION 0, 0, 6, 0
FILEFLAGS 0x0
FILEVERSION 0, 0, 6, 0
{
BLOCK "StringFileInfo"
{
BLOCK "000004b0"
{
VALUE "ProductVersion", "0.0.6"
VALUE "ProductName", "teralauncher"
VALUE "LegalCopyright", "Tera Launcher"
VALUE "FileVersion", "0.0.6"
VALUE "FileDescription", "A Tauri Launcher for Tera Pserver"
}
}
BLOCK "VarFileInfo" {
VALUE "Translation", 0x0, 0x04b0
}
}
32512 ICON "\\\\?\\D:\\Takumi\\Tool\\Launch\\teralaunch\\src-tauri\\icons\\tera_server.ico"
1 24
{
" <assembly xmlns=""urn:schemas-microsoft-com:asm.v1"" manifestVersion=""1.0""> "
" <dependency> "
" <dependentAssembly> "
" <assemblyIdentity "
" type=""win32"" "
" name=""Microsoft.Windows.Common-Controls"" "
" version=""*******"" "
" processorArchitecture=""*"" "
" publicKeyToken=""6595b64144ccf1df"" "
" language=""*"" "
" /> "
" </dependentAssembly> "
" </dependency> "
" </assembly> "
}

