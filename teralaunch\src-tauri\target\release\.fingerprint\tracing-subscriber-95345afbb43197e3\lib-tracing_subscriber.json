{"rustc": 1842507548689473721, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 7913717744677543082, "profile": 2040997289075261528, "path": 8291859966711128186, "deps": [[1009387600818341822, "matchers", false, 14031491253325320611], [1017461770342116999, "sharded_slab", false, 1830412912854118330], [1076501750996383263, "once_cell", false, 3317331631347182004], [6831611227313043439, "smallvec", false, 8635033658783787568], [8614575489689151157, "nu_ansi_term", false, 11890852882701139692], [8800316697867969272, "regex", false, 8106054280523136889], [10806489435541507125, "tracing_log", false, 9427869276203229872], [12427285511609802057, "thread_local", false, 11426048195192931422], [12756065109715636409, "tracing_core", false, 4483897827743744380], [14626413149905853098, "tracing", false, 15162072962576219282]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tracing-subscriber-95345afbb43197e3\\dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}