# Tera Launcher 测试服务器

这是一个用于测试 Tera 启动器的本地 HTTP 服务器。它模拟了启动器所需的所有 API 端点。

## 功能特性

- 🌐 **服务器列表 API** - 提供游戏服务器列表
- 🔐 **登录 API** - 处理用户登录请求
- 📁 **文件哈希 API** - 提供文件更新信息
- 📥 **文件下载 API** - 模拟游戏文件下载
- 📊 **状态页面** - 显示服务器状态和可用端点

## 快速开始

### 方法 1: 使用批处理文件 (推荐)
```bash
# 双击运行
start-server.bat
```

### 方法 2: 直接运行 Python
```bash
# 确保已安装 Python 3.6+
python server.py
```

## 服务器端点

服务器运行在 `http://localhost:8000`，提供以下端点：

### GET 端点
- `/` - 服务器状态页面
- `/tera/ServerList.json?lang=en&sort=3` - 获取服务器列表
- `/tera/launcher/hash-file.json` - 获取文件哈希信息
- `/public/files/*` - 下载游戏文件

### POST 端点
- `/tera/LauncherLoginAction` - 处理登录请求

## 测试步骤

1. **启动测试服务器**
   ```bash
   cd test-server
   python server.py
   ```

2. **验证服务器运行**
   - 打开浏览器访问: http://localhost:8000
   - 应该看到服务器状态页面

3. **测试 API 端点**
   - 服务器列表: http://localhost:8000/tera/ServerList.json?lang=en&sort=3
   - 哈希文件: http://localhost:8000/tera/launcher/hash-file.json

4. **启动 Tera 启动器**
   ```bash
   cd ../teralaunch
   npm run tauri dev
   ```

5. **测试启动器功能**
   - 登录功能
   - 服务器列表加载
   - 文件更新检查

## 配置说明

启动器配置已自动更新为指向本地测试服务器：

```json
{
  "LOGIN_ACTION_URL": "http://localhost:8000/tera/LauncherLoginAction",
  "HASH_FILE_URL": "http://localhost:8000/tera/launcher/hash-file.json", 
  "FILE_SERVER_URL": "http://localhost:8000/public",
  "SERVER_LIST_URL": "http://localhost:8000/tera/ServerList.json?lang=en&sort=3"
}
```

## 模拟数据

### 服务器列表
- Test Server (PvE) - 127.0.0.1:10001
- Dev Server (PvP) - 127.0.0.1:10002

### 登录信息
- 任何用户名/密码组合都会成功登录
- 返回测试用户信息 (user_no: 12345)

### 文件更新
- 提供示例文件哈希信息
- 支持模拟文件下载

## 故障排除

### 端口被占用
如果 8000 端口被占用，可以修改 `server.py` 中的端口号：
```python
run_server(port=8001)  # 改为其他端口
```

### Python 未安装
确保系统已安装 Python 3.6 或更高版本：
```bash
python --version
```

### 启动器连接失败
1. 确认测试服务器正在运行
2. 检查防火墙设置
3. 验证配置文件中的 URL 是否正确

## 日志输出

服务器会显示所有请求的日志：
```
[14:30:15] GET /tera/ServerList.json?lang=en&sort=3
[14:30:16] POST /tera/LauncherLoginAction
[14:30:17] GET /tera/launcher/hash-file.json
```

## 扩展功能

可以根据需要修改 `server.py` 来：
- 添加更多测试数据
- 模拟错误情况
- 添加认证逻辑
- 记录详细日志

## 注意事项

- 这是一个测试服务器，不适用于生产环境
- 所有数据都是模拟的，不会保存任何信息
- 服务器重启后所有状态都会重置
