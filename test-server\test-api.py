#!/usr/bin/env python3
"""
API测试脚本 - 测试所有Tera启动器API端点
"""

import requests
import json
import sys
from datetime import datetime

BASE_URL = "http://localhost:8000"

def test_endpoint(method, endpoint, data=None, description=""):
    """测试单个API端点"""
    url = f"{BASE_URL}{endpoint}"
    
    print(f"\n{'='*60}")
    print(f"测试: {description}")
    print(f"方法: {method}")
    print(f"URL: {url}")
    print(f"{'='*60}")
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, timeout=10)
        elif method.upper() == "POST":
            headers = {'Content-Type': 'application/json'}
            response = requests.post(url, json=data, headers=headers, timeout=10)
        else:
            print(f"❌ 不支持的HTTP方法: {method}")
            return False
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        # 尝试解析JSON响应
        try:
            json_data = response.json()
            print(f"响应内容 (JSON):")
            print(json.dumps(json_data, indent=2, ensure_ascii=False))
        except:
            print(f"响应内容 (文本):")
            print(response.text[:500] + "..." if len(response.text) > 500 else response.text)
        
        if response.status_code == 200:
            print("✅ 测试通过")
            return True
        else:
            print(f"⚠️ 状态码不是200: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
        return False

def main():
    """运行所有API测试"""
    print("🚀 开始测试Tera启动器API端点")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 服务器地址: {BASE_URL}")
    
    tests = [
        {
            "method": "GET",
            "endpoint": "/",
            "description": "服务器状态页面"
        },
        {
            "method": "GET", 
            "endpoint": "/tera/ServerList.json?lang=en&sort=3",
            "description": "获取服务器列表"
        },
        {
            "method": "GET",
            "endpoint": "/tera/launcher/hash-file.json", 
            "description": "获取文件哈希信息"
        },
        {
            "method": "POST",
            "endpoint": "/tera/LauncherLoginAction",
            "data": {
                "username": "testuser",
                "password": "testpass"
            },
            "description": "用户登录"
        },
        {
            "method": "GET",
            "endpoint": "/public/files/S1Game/Config/test.ini",
            "description": "文件下载测试"
        }
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        success = test_endpoint(
            test["method"],
            test["endpoint"], 
            test.get("data"),
            test["description"]
        )
        if success:
            passed += 1
    
    print(f"\n{'='*60}")
    print(f"📊 测试结果汇总")
    print(f"{'='*60}")
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"成功率: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试都通过了！")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查服务器状态")
        return 1

if __name__ == "__main__":
    sys.exit(main())
