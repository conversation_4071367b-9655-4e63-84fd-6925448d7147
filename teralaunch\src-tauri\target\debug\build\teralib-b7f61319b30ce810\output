cargo:rerun-if-changed=src/serverlist.proto
OUT_DIR: D:\Takumi\Tool\Launch\teralaunch\src-tauri\target\debug\build\teralib-b7f61319b30ce810\out
Proto compilation failed: Custom { kind: NotFound, error: "Could not find `protoc`. If `protoc` is installed, try setting the `PROTOC` environment variable to the path of the `protoc` binary. Try installing `protobuf-compiler` or `protobuf` using your package manager. It is also available at https://github.com/protocolbuffers/protobuf/releases  For more information: https://docs.rs/prost-build/#sourcing-protoc" }
