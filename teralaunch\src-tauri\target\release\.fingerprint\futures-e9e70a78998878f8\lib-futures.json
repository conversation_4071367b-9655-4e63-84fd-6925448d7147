{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 6120535526081445694, "profile": 2040997289075261528, "path": 1888068284453390626, "deps": [[4824058070882510515, "futures_io", false, 1027552590398698199], [10430468135454803800, "futures_task", false, 9365873646997170634], [11913130400938634928, "futures_util", false, 7705237049435048172], [12588177665552295757, "futures_core", false, 2275492903305144867], [14760549632560668547, "futures_executor", false, 50909917286021717], [17397492013442894880, "futures_channel", false, 6913063691792053404], [18223668634047601185, "futures_sink", false, 4894546576601515444]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\futures-e9e70a78998878f8\\dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}