{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"__private_docs\", \"default\", \"form\", \"headers\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 12074263998246110377, "profile": 2040997289075261528, "path": 565762688068940322, "deps": [[40386456601120721, "percent_encoding", false, 2129877517113298051], [264090853244900308, "sync_wrapper", false, 15681497232691445498], [1133100163585637996, "tower_service", false, 2974626735743812269], [3129130049864710036, "memchr", false, 15400894219084788294], [3601586811267292532, "tower", false, 16897663686790590506], [3664886486575944113, "tower_layer", false, 17445866395189721613], [4405182208873388884, "http", false, 10162976284368013428], [4800206021143169329, "pin_project_lite", false, 17094343566241858788], [5459299554234637951, "async_trait", false, 6555924764327405129], [8915503303801890683, "http_body", false, 7254496216998061372], [9293824762099617471, "axum_core", false, 3881854747073117321], [9678799920983747518, "matchit", false, 6925620075877684089], [10229185211513642314, "mime", false, 9651018063964816897], [10435729446543529114, "bitflags", false, 10196396877431311167], [11356286270989087318, "itoa", false, 11261267015963816697], [11510052217253957479, "hyper", false, 4481948226216532972], [11913130400938634928, "futures_util", false, 7705237049435048172], [16227728351758841112, "bytes", false, 15932244784508788198], [16244562316228021087, "build_script_build", false, 5050822348568310691], [17174345729924723953, "serde", false, 10483066152181423585]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\axum-33104df2c4bc74d8\\dep-lib-axum", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}